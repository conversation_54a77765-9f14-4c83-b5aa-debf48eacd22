#!/usr/bin/env python3
"""
PropBolt API Server
Production API server for real estate data endpoints - Complete 13 Endpoint Implementation.
Enhanced with PostgreSQL-based API key management while maintaining backward compatibility.
"""

import os
import sys
from typing import Optional, List
import datetime
from fastapi import FastAP<PERSON>, Header, HTTPEx<PERSON>, Request, Depends
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# Add local modules to path
sys.path.append('./propbolt_py')

# Import database components
from database.middleware import APIKeyAuthMiddleware, api_key_dependency
from database.connection import startup_database, shutdown_database, check_database_health
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    from propbolt_py.client import Client as PropBoltClient
    CLIENTS_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Could not import SDK client: {e}")
    print("Running in mock mode...")
    CLIENTS_AVAILABLE = False

# Lifespan event handler (replaces deprecated on_event)
from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Handle startup and shutdown events"""
    # Startup
    try:
        await startup_database()
        logger.info("✅ Database connections initialized")
    except Exception as e:
        logger.error(f"❌ Database initialization failed: {e}")
        # Don't fail startup - fallback to getenv approach
        logger.info("🔄 Continuing with fallback API key authentication")

    yield

    # Shutdown
    try:
        await shutdown_database()
        logger.info("✅ Database connections closed")
    except Exception as e:
        logger.error(f"❌ Database cleanup failed: {e}")

# Initialize FastAPI app with lifespan
app = FastAPI(
    title="PropBolt API",
    description="Real Estate Data API Service - Complete 13 Endpoint Implementation with PostgreSQL API Key Management",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://data.propbolt.com", "https://propbolt.com", "http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add API Key Authentication Middleware
# This maintains backward compatibility with existing getenv approach
app.add_middleware(
    APIKeyAuthMiddleware,
    exclude_paths=["/docs", "/redoc", "/openapi.json", "/health", "/", "/admin"]
)

# Lifespan is now configured in the FastAPI constructor above

# Initialize API clients
API_KEY = os.getenv("API_KEY", "AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914")

if CLIENTS_AVAILABLE:
    try:
        propbolt_client = PropBoltClient(api_key=API_KEY)
        print("✅ PropBolt SDK client initialized successfully")
    except Exception as e:
        print(f"❌ Error initializing PropBolt SDK client: {e}")
        CLIENTS_AVAILABLE = False
        propbolt_client = None
else:
    propbolt_client = None

# Pydantic Models for Request/Response
class IntRange(BaseModel):
    min: Optional[int] = None
    max: Optional[int] = None
class PropertyDetailRequest(BaseModel):
    address: Optional[str] = None
    id: Optional[str] = None
    apn: Optional[str] = None
    city: Optional[str] = None
    county: Optional[str] = None
    state: Optional[str] = None
    zip: Optional[str] = None
    house: Optional[str] = None
    street: Optional[str] = None
    unit: Optional[str] = None
    fips: Optional[str] = None
    exact_match: Optional[bool] = False
    comps: Optional[bool] = False

class PropertySearchRequest(BaseModel):
    address: Optional[str] = None
    city: Optional[str] = None
    county: Optional[str] = None
    state: Optional[str] = None
    zip: Optional[str] = None
    beds: Optional[IntRange] = None
    baths: Optional[IntRange] = None
    value: Optional[IntRange] = None
    size: Optional[int] = 50
    result_index: Optional[int] = 0

class PropertyBulkRequest(BaseModel):
    ids: List[str]

class PropertyCompsRequest(BaseModel):
    address: Optional[str] = None
    id: Optional[str] = None

class PropertyCompsAdvancedRequest(BaseModel):
    address: Optional[str] = None
    id: Optional[str] = None
    max_radius_miles: Optional[float] = 1.0
    max_days_back: Optional[int] = 180
    max_results: Optional[int] = 10
    exact_match: Optional[bool] = False

class AutoCompleteRequest(BaseModel):
    search: str
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    precision: Optional[int] = None
    search_types: Optional[List[str]] = None

class AddressVerificationRequest(BaseModel):
    addresses: List[dict]
    strict: Optional[bool] = False

class PropGPTRequest(BaseModel):
    query: str
    size: Optional[int] = 50
    model: Optional[str] = "gpt-4o"

class CSVBuilderRequest(BaseModel):
    file_name: str
    map: List[str]
    webcomplete_url: Optional[str] = None

class PropertyAVMRequest(BaseModel):
    address: Optional[str] = None
    id: Optional[str] = None
    strict: Optional[bool] = False

class PropertyLiensRequest(BaseModel):
    address: Optional[str] = None
    id: Optional[str] = None
    zip: Optional[str] = None
    apn: Optional[str] = None
    fips: Optional[str] = None

class PropertyMappingRequest(BaseModel):
    address: Optional[str] = None
    city: Optional[str] = None
    county: Optional[str] = None
    state: Optional[str] = None
    zip: Optional[str] = None
    size: Optional[int] = 50

class PropertyParcelRequest(BaseModel):
    address: Optional[str] = None
    apn: Optional[str] = None
    city: Optional[str] = None
    county: Optional[str] = None
    fips: Optional[str] = None
    house: Optional[str] = None
    id: Optional[str] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    radius: Optional[float] = None
    result_index: Optional[int] = None
    size: Optional[int] = None
    state: Optional[str] = None
    street: Optional[str] = None
    unit: Optional[str] = None
    zip: Optional[str] = None

# Authentication middleware
def verify_api_key(x_api_key: Optional[str] = Header(None)):
    if not x_api_key:
        raise HTTPException(status_code=401, detail="API key required")
    expected_key = os.getenv("API_KEY", "AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914")
    if x_api_key != expected_key:
        raise HTTPException(status_code=401, detail="Invalid API key")
    return x_api_key

# Helper function to handle API errors
def handle_api_error(e: Exception, endpoint: str):
    print(f"Error in {endpoint}: {str(e)}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal Server Error",
            "message": f"Error calling {endpoint}: {str(e)}",
            "timestamp": datetime.datetime.now().isoformat()
        }
    )

# Mock response for when SDK clients are not available
def get_mock_response(endpoint: str):
    return {
        "success": True,
        "data": {
            "message": f"Mock response for {endpoint}",
            "note": "SDK clients not available - this is a mock response",
            "endpoint": endpoint,
            "status": "mock_mode"
        },
        "timestamp": datetime.datetime.now().isoformat()
    }

# API Endpoints
@app.get("/")
async def root():
    """API Information and available endpoints"""
    return {
        "message": "PropBolt Real Estate API",
        "version": "1.0.0",
        "description": "Complete 13 Endpoint Implementation",
        "endpoints": {
            "health": "GET /health",
            "property_search": "POST /v2/PropertySearch",
            "property_details": "POST /v2/PropertyDetail", 
            "property_detail_bulk": "POST /v2/PropertyDetailBulk",
            "property_parcel": "POST /v1/PropertyParcel",
            "property_comps_v2": "POST /v2/PropertyComps",
            "property_comps_v3": "POST /v3/PropertyComps",
            "autocomplete": "POST /v2/AutoComplete",
            "address_verification": "POST /v2/AddressVerification",
            "propgpt": "POST /v2/PropGPT",
            "csv_builder": "POST /v2/CSVBuilder",
            "property_avm": "POST /v2/PropertyAvm",
            "property_liens": "POST /v2/Reports/PropertyLiens",
            "property_mapping": "POST /v2/PropertyMapping"
        },
        "documentation": "/docs",
        "timestamp": datetime.datetime.now().isoformat()
    }

@app.get("/health")
async def health_check():
    """Enhanced health check endpoint with database status"""
    # Check database health
    try:
        db_health = await check_database_health()
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        db_health = {"database": {"overall": "unhealthy", "error": str(e)}}

    return {
        "status": "healthy",
        "timestamp": datetime.datetime.now().isoformat(),
        "version": "1.0.0",
        "api_key_configured": bool(API_KEY),
        "sdk_clients_available": CLIENTS_AVAILABLE,
        "mode": "production" if CLIENTS_AVAILABLE else "mock",
        "api_key_management": "database" if db_health.get("database", {}).get("overall") == "healthy" else "fallback",
        **db_health
    }

@app.get("/admin/health")
async def admin_health_check():
    """Admin health check endpoint for testing"""
    # Simple admin health check for testing purposes
    return {
        "status": "healthy",
        "service": "PropBolt API Admin",
        "timestamp": datetime.datetime.now().isoformat(),
        "message": "Admin interface is accessible"
    }

# 1. Property Search API
@app.post("/v2/PropertySearch")
async def property_search(
    request: PropertySearchRequest,
    x_api_key: str = Header(..., description="API Key")
):
    """Property Search API - Searchable API for list building and filtering"""
    verify_api_key(x_api_key)

    if not CLIENTS_AVAILABLE or not propbolt_client:
        return get_mock_response("PropertySearch")

    try:
        # Convert request to SDK parameters
        search_params = {k: v for k, v in request.model_dump().items() if v is not None}

        # Call the PropBolt API
        response = propbolt_client.v2.property_search.create(**search_params)

        return {
            "success": True,
            "data": response.data if hasattr(response, 'data') else response,
            "timestamp": datetime.datetime.now().isoformat()
        }
    except Exception as e:
        return handle_api_error(e, "PropertySearch")

# 2. Property Detail API
@app.post("/v2/PropertyDetail")
async def property_detail(
    request: PropertyDetailRequest,
    x_api_key: str = Header(..., description="API Key")
):
    """Property Detail API - Get comprehensive property information"""
    verify_api_key(x_api_key)

    if not CLIENTS_AVAILABLE or not propbolt_client:
        return get_mock_response("PropertyDetail")

    try:
        # Convert request to SDK parameters
        detail_params = {k: v for k, v in request.model_dump().items() if v is not None}

        # Call the PropBolt API
        response = propbolt_client.v2.property_detail.create(**detail_params)

        return {
            "success": True,
            "data": response.data if hasattr(response, 'data') else response,
            "timestamp": datetime.datetime.now().isoformat()
        }
    except Exception as e:
        return handle_api_error(e, "PropertyDetail")

# 3. Property Detail Bulk API
@app.post("/v2/PropertyDetailBulk")
async def property_detail_bulk(
    request: PropertyBulkRequest,
    x_api_key: str = Header(..., description="API Key")
):
    """Property Detail Bulk API - Retrieve up to 1000 properties at once"""
    verify_api_key(x_api_key)

    if not CLIENTS_AVAILABLE or not propbolt_client:
        return get_mock_response("PropertyDetailBulk")

    try:
        # Call the PropBolt API
        response = propbolt_client.v2.property_detail_bulk.create(ids=request.ids)
        
        return {
            "success": True,
            "data": response.data if hasattr(response, 'data') else response,
            "timestamp": datetime.datetime.now().isoformat()
        }
    except Exception as e:
        return handle_api_error(e, "PropertyDetailBulk")

# 4. Property Parcel API
@app.post("/v1/PropertyParcel")
async def property_parcel(
    request: PropertyParcelRequest,
    x_api_key: str = Header(..., description="API Key")
):
    """Property Boundary API - Returns parcel boundaries in GEOJSON format"""
    verify_api_key(x_api_key)

    if not CLIENTS_AVAILABLE or not propbolt_client:
        return get_mock_response("PropertyParcel")

    try:
        # Convert request to SDK parameters
        parcel_params = {k: v for k, v in request.model_dump().items() if v is not None}

        # Call the PropBolt API
        response = propbolt_client.v1.property_parcel.create(**parcel_params)

        return {
            "success": True,
            "data": response.data if hasattr(response, 'data') else response,
            "timestamp": datetime.datetime.now().isoformat()
        }
    except Exception as e:
        return handle_api_error(e, "PropertyParcel")

# 5. Property Comparables API v2
@app.post("/v2/PropertyComps")
async def property_comps(
    request: PropertyCompsRequest,
    x_api_key: str = Header(..., description="API Key")
):
    """Property Comparables API v2 - Generate property comparables for valuation"""
    verify_api_key(x_api_key)

    if not CLIENTS_AVAILABLE or not propbolt_client:
        return get_mock_response("PropertyComps")

    try:
        # Convert request to SDK parameters
        comps_params = {k: v for k, v in request.model_dump().items() if v is not None}

        # Call the PropBolt API
        response = propbolt_client.v2.property_comps.create(**comps_params)

        return {
            "success": True,
            "data": response.data if hasattr(response, 'data') else response,
            "timestamp": datetime.datetime.now().isoformat()
        }
    except Exception as e:
        return handle_api_error(e, "PropertyComps")

# 6. Property Comparables API v3
@app.post("/v3/PropertyComps")
async def property_comps_advanced(
    request: PropertyCompsAdvancedRequest,
    x_api_key: str = Header(..., description="API Key")
):
    """Property Comparables API v3 - Advanced comparables with customizable parameters"""
    verify_api_key(x_api_key)

    if not CLIENTS_AVAILABLE or not propbolt_client:
        return get_mock_response("PropertyCompsAdvanced")

    try:
        # Convert request to SDK parameters
        comps_params = {k: v for k, v in request.model_dump().items() if v is not None}

        # Call the PropBolt API
        response = propbolt_client.v3.property_comps.create(**comps_params)

        return {
            "success": True,
            "data": response.data if hasattr(response, 'data') else response,
            "timestamp": datetime.datetime.now().isoformat()
        }
    except Exception as e:
        return handle_api_error(e, "PropertyCompsAdvanced")

# 7. AutoComplete API
@app.post("/v2/AutoComplete")
async def autocomplete(
    request: AutoCompleteRequest,
    x_api_key: str = Header(..., description="API Key")
):
    """AutoComplete API - Property search based on incomplete address parts"""
    verify_api_key(x_api_key)

    if not CLIENTS_AVAILABLE or not propbolt_client:
        return get_mock_response("AutoComplete")

    try:
        # Convert request to SDK parameters
        autocomplete_params = {k: v for k, v in request.model_dump().items() if v is not None}

        # Call the PropBolt API
        response = propbolt_client.v2.auto_complete.create(**autocomplete_params)

        return {
            "success": True,
            "data": response.data if hasattr(response, 'data') else response,
            "timestamp": datetime.datetime.now().isoformat()
        }
    except Exception as e:
        return handle_api_error(e, "AutoComplete")

# 8. Address Verification API
@app.post("/v2/AddressVerification")
async def address_verification(
    request: AddressVerificationRequest,
    x_api_key: str = Header(..., description="API Key")
):
    """Address Verification API - Verify 1-100 addresses for accuracy"""
    verify_api_key(x_api_key)

    if not CLIENTS_AVAILABLE or not propbolt_client:
        return get_mock_response("AddressVerification")

    try:
        # Convert request to SDK parameters
        verify_params = {k: v for k, v in request.model_dump().items() if v is not None}

        # Call the PropBolt API
        response = propbolt_client.v2.address_verification.create(**verify_params)

        return {
            "success": True,
            "data": response.data if hasattr(response, 'data') else response,
            "timestamp": datetime.datetime.now().isoformat()
        }
    except Exception as e:
        return handle_api_error(e, "AddressVerification")

# 9. PropGPT API
@app.post("/v2/PropGPT")
async def propgpt(
    request: PropGPTRequest,
    x_api_key: str = Header(..., description="API Key"),
    x_openai_key: str = Header(..., description="OpenAI API Key")
):
    """PropGPT API - Natural language property search using AI"""
    verify_api_key(x_api_key)

    if not CLIENTS_AVAILABLE or not propbolt_client:
        return get_mock_response("PropGPT")

    try:
        # Convert request to SDK parameters
        gpt_params = {k: v for k, v in request.model_dump().items() if v is not None}

        # Call the PropBolt API
        response = propbolt_client.v2.prop_gpt.create(
            x_api_key=x_api_key,
            x_openai_key=x_openai_key,
            **gpt_params
        )

        return {
            "success": True,
            "data": response.data if hasattr(response, 'data') else response,
            "timestamp": datetime.datetime.now().isoformat()
        }
    except Exception as e:
        return handle_api_error(e, "PropGPT")

# 10. CSV Builder API
@app.post("/v2/CSVBuilder")
async def csv_builder(
    request: CSVBuilderRequest,
    x_api_key: str = Header(..., description="API Key")
):
    """CSV Generator API - Generate CSV exports of property data"""
    verify_api_key(x_api_key)

    if not CLIENTS_AVAILABLE or not propbolt_client:
        return get_mock_response("CSVBuilder")

    try:
        # Convert request to SDK parameters
        csv_params = {k: v for k, v in request.model_dump().items() if v is not None}

        # Call the PropBolt API
        response = propbolt_client.v2.csv_builder.create(**csv_params)

        return {
            "success": True,
            "data": response.data if hasattr(response, 'data') else response,
            "timestamp": datetime.datetime.now().isoformat()
        }
    except Exception as e:
        return handle_api_error(e, "CSVBuilder")

# 11. Property AVM API
@app.post("/v2/PropertyAvm")
async def property_avm(
    request: PropertyAVMRequest,
    x_api_key: str = Header(..., description="API Key")
):
    """Lender Grade AVM API - Get precise property valuations"""
    verify_api_key(x_api_key)

    if not CLIENTS_AVAILABLE or not propbolt_client:
        return get_mock_response("PropertyAVM")

    try:
        # Convert request to SDK parameters
        avm_params = {k: v for k, v in request.model_dump().items() if v is not None}

        # Call the PropBolt API
        response = propbolt_client.v2.property_avm.create(**avm_params)

        return {
            "success": True,
            "data": response.data if hasattr(response, 'data') else response,
            "timestamp": datetime.datetime.now().isoformat()
        }
    except Exception as e:
        return handle_api_error(e, "PropertyAvm")

# 12. Property Liens API
@app.post("/v2/Reports/PropertyLiens")
async def property_liens(
    request: PropertyLiensRequest,
    x_api_key: str = Header(..., description="API Key")
):
    """Involuntary Liens API - Get property lien information"""
    verify_api_key(x_api_key)

    if not CLIENTS_AVAILABLE or not propbolt_client:
        return get_mock_response("PropertyLiens")

    try:
        # Convert request to SDK parameters
        liens_params = {k: v for k, v in request.model_dump().items() if v is not None}

        # Call the PropBolt API
        response = propbolt_client.v2.reports.property_liens.create(**liens_params)

        return {
            "success": True,
            "data": response.data if hasattr(response, 'data') else response,
            "timestamp": datetime.datetime.now().isoformat()
        }
    except Exception as e:
        return handle_api_error(e, "PropertyLiens")

# 13. Property Mapping API
@app.post("/v2/PropertyMapping")
async def property_mapping(
    request: PropertyMappingRequest,
    x_api_key: str = Header(..., description="API Key")
):
    """Mapping (Pins) API - Create map pins for PropTech applications"""
    verify_api_key(x_api_key)

    if not CLIENTS_AVAILABLE or not propbolt_client:
        return get_mock_response("PropertyMapping")

    try:
        # Convert request to SDK parameters
        mapping_params = {k: v for k, v in request.model_dump().items() if v is not None}

        # Call the PropBolt API
        response = propbolt_client.v2.property_mapping.create(**mapping_params)

        return {
            "success": True,
            "data": response.data if hasattr(response, 'data') else response,
            "timestamp": datetime.datetime.now().isoformat()
        }
    except Exception as e:
        return handle_api_error(e, "PropertyMapping")

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal Server Error",
            "message": str(exc),
            "timestamp": datetime.datetime.now().isoformat(),
            "path": str(request.url)
        }
    )

if __name__ == "__main__":
    port = int(os.getenv("PORT", 8080))  # App Engine sets PORT automatically
    print("🚀 Starting PropBolt API Server...")
    print(f"📍 Server will be available at: http://localhost:{port}")
    print(f"📖 API Documentation: http://localhost:{port}/docs")
    print(f"🔍 Health Check: http://localhost:{port}/health")
    print(f"🔧 Admin Interface: http://localhost:{port}/admin/docs")
    print(f"\n💡 API Key: {API_KEY}")
    print(f"🔑 Admin Key: {os.getenv('ADMIN_API_KEY', 'admin-' + API_KEY)}")
    print("⏹️  Press Ctrl+C to stop the server\n")

    uvicorn.run("main:app", host="0.0.0.0", port=port, reload=False)
